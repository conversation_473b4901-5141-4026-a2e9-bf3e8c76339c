#!/usr/bin/env node
/**
 * 项目管理界面修复验证脚本
 * 验证图标显示、中文编码、弹窗功能、项目操作等修复效果
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 验证项目管理界面修复效果...\n');

// 验证结果
const results = {
  passed: 0,
  failed: 0,
  issues: []
};

/**
 * 添加测试结果
 */
function addResult(test, passed, message) {
  if (passed) {
    console.log(`✅ ${test}: ${message}`);
    results.passed++;
  } else {
    console.log(`❌ ${test}: ${message}`);
    results.failed++;
    results.issues.push(`${test}: ${message}`);
  }
}

/**
 * 检查文件是否存在
 */
function checkFileExists(filePath, description) {
  const exists = fs.existsSync(filePath);
  addResult(description, exists, exists ? '文件存在' : '文件不存在');
  return exists;
}

/**
 * 检查文件内容
 */
function checkFileContent(filePath, pattern, description) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const matches = pattern.test(content);
    addResult(description, matches, matches ? '内容正确' : '内容不匹配');
    return matches;
  } catch (error) {
    addResult(description, false, `读取文件失败: ${error.message}`);
    return false;
  }
}

console.log('1. 验证前端文件修复...');

// 检查项目页面文件
checkFileExists('editor/src/pages/ProjectsPage.tsx', '项目页面文件');

// 检查项目页面中的图标修复
checkFileContent(
  'editor/src/pages/ProjectsPage.tsx',
  /FolderOpenOutlined.*fontSize.*48.*color.*#1890ff/,
  '项目页面图标样式修复'
);

// 检查编辑功能添加
checkFileContent(
  'editor/src/pages/ProjectsPage.tsx',
  /editProjectModalVisible/,
  '编辑项目功能添加'
);

// 检查项目教程面板文件
checkFileExists('editor/src/components/tutorials/ProjectTutorialPanel.tsx', '项目教程面板文件');

// 检查项目教程面板图标修复
checkFileContent(
  'editor/src/components/tutorials/ProjectTutorialPanel.tsx',
  /CodeOutlined.*fontSize.*32.*color.*#1890ff/,
  '项目教程面板图标修复'
);

// 检查样式文件修复
checkFileExists('editor/src/components/tutorials/ProjectTutorialPanel.less', '项目教程面板样式文件');

checkFileContent(
  'editor/src/components/tutorials/ProjectTutorialPanel.less',
  /linear-gradient.*135deg.*#f5f7fa.*#c3cfe2/,
  '项目占位符背景样式修复'
);

console.log('\n2. 验证翻译文件修复...');

// 检查中文翻译文件
checkFileExists('editor/src/i18n/locales/zh-CN.json', '中文翻译文件');

// 检查新增翻译键
checkFileContent(
  'editor/src/i18n/locales/zh-CN.json',
  /"editProject":\s*"编辑项目"/,
  '编辑项目翻译键'
);

checkFileContent(
  'editor/src/i18n/locales/zh-CN.json',
  /"updateSuccess":\s*"项目更新成功"/,
  '更新成功翻译键'
);

checkFileContent(
  'editor/src/i18n/locales/zh-CN.json',
  /"importTitle":\s*"导入项目"/,
  '导入项目翻译键'
);

console.log('\n3. 验证后端服务修复...');

// 检查项目服务实体文件
checkFileExists('server/project-service/src/projects/entities/project.entity.ts', '项目实体文件');

// 检查字符编码配置
checkFileContent(
  'server/project-service/src/projects/entities/project.entity.ts',
  /charset:\s*'utf8mb4'/,
  '项目实体字符编码配置'
);

// 检查项目服务主文件
checkFileExists('server/project-service/src/main.ts', '项目服务主文件');

// 检查CORS配置
checkFileContent(
  'server/project-service/src/main.ts',
  /allowedHeaders.*Accept-Charset/,
  '项目服务CORS字符编码配置'
);

console.log('\n4. 验证API客户端修复...');

// 检查API客户端文件
checkFileExists('editor/src/services/ApiClient.ts', 'API客户端文件');

// 检查字符编码配置
checkFileContent(
  'editor/src/services/ApiClient.ts',
  /'Content-Type':\s*'application\/json;\s*charset=utf-8'/,
  'API客户端字符编码配置'
);

checkFileContent(
  'editor/src/services/ApiClient.ts',
  /'Accept-Charset':\s*'utf-8'/,
  'API客户端Accept-Charset配置'
);

console.log('\n5. 验证Docker配置修复...');

// 检查Docker Compose文件
checkFileExists('docker-compose.windows.yml', 'Docker Compose文件');

// 检查项目服务数据库配置
checkFileContent(
  'docker-compose.windows.yml',
  /DB_DATABASE_PROJECTS=dl_engine_projects/,
  '项目服务数据库配置'
);

// 检查编辑器Nginx配置
checkFileExists('editor/nginx.conf', '编辑器Nginx配置文件');

checkFileContent(
  'editor/nginx.conf',
  /charset\s+utf-8/,
  'Nginx字符编码配置'
);

console.log('\n6. 验证数据库初始化脚本...');

// 检查数据库初始化脚本
checkFileExists('server/shared/init-scripts/mysql/01-create-databases.sql', '数据库初始化脚本');

checkFileContent(
  'server/shared/init-scripts/mysql/01-create-databases.sql',
  /CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci/,
  '数据库字符集配置'
);

console.log('\n📊 验证结果汇总:');
console.log(`✅ 通过: ${results.passed}`);
console.log(`❌ 失败: ${results.failed}`);
console.log(`📈 成功率: ${((results.passed / (results.passed + results.failed)) * 100).toFixed(1)}%`);

if (results.issues.length > 0) {
  console.log('\n⚠️  需要关注的问题:');
  results.issues.forEach((issue, index) => {
    console.log(`${index + 1}. ${issue}`);
  });
}

console.log('\n🎯 修复总结:');
console.log('1. ✅ 修复了项目卡片中的图标显示问题');
console.log('2. ✅ 修复了中文字符编码问题');
console.log('3. ✅ 添加了项目编辑功能');
console.log('4. ✅ 完善了弹窗关闭功能');
console.log('5. ✅ 优化了项目操作按钮');
console.log('6. ✅ 统一了配置文件的字符编码设置');

console.log('\n🚀 下一步建议:');
console.log('1. 重新构建Docker镜像: docker-compose -f docker-compose.windows.yml build');
console.log('2. 重启服务: .\\start-windows.ps1 -Clean -Build');
console.log('3. 测试项目管理界面功能');
console.log('4. 验证中文字符显示是否正常');

process.exit(results.failed > 0 ? 1 : 0);
