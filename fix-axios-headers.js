#!/usr/bin/env node

/**
 * 修复前端服务中的axios请求头问题
 * 移除不安全的请求头，统一使用ApiClient
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 修复前端服务中的axios请求头问题...\n');

// 需要修复的文件列表
const filesToFix = [
  'editor/src/services/AssetService.ts',
  'editor/src/services/SceneService.ts',
  'editor/src/services/GitService.ts',
  'editor/src/services/CollaborationService.ts'
];

// 修复函数
function fixAxiosUsage(filePath) {
  if (!fs.existsSync(filePath)) {
    console.log(`⚠️  文件不存在: ${filePath}`);
    return;
  }

  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let changed = false;

    // 替换axios导入为apiClient导入
    if (content.includes("import axios from 'axios';")) {
      content = content.replace(
        /import axios from 'axios';\s*\n/g,
        ''
      );
      
      // 添加apiClient导入（如果还没有）
      if (!content.includes("import { apiClient } from './ApiClient';")) {
        const importMatch = content.match(/^(import[^;]+;[\s\n]*)+/);
        if (importMatch) {
          const importSection = importMatch[0];
          const newImportSection = importSection + "import { apiClient } from './ApiClient';\n";
          content = content.replace(importSection, newImportSection);
        }
      }
      changed = true;
    }

    // 替换axios.get调用
    content = content.replace(
      /axios\.get\(`\$\{config\.apiUrl\}([^`]+)`/g,
      "apiClient.get('$1'"
    );

    // 替换axios.post调用
    content = content.replace(
      /axios\.post\(`\$\{config\.apiUrl\}([^`]+)`/g,
      "apiClient.post('$1'"
    );

    // 替换axios.put调用
    content = content.replace(
      /axios\.put\(`\$\{config\.apiUrl\}([^`]+)`/g,
      "apiClient.put('$1'"
    );

    // 替换axios.patch调用
    content = content.replace(
      /axios\.patch\(`\$\{config\.apiUrl\}([^`]+)`/g,
      "apiClient.patch('$1'"
    );

    // 替换axios.delete调用
    content = content.replace(
      /axios\.delete\(`\$\{config\.apiUrl\}([^`]+)`/g,
      "apiClient.delete('$1'"
    );

    // 替换直接的axios调用（不带config.apiUrl）
    content = content.replace(
      /axios\.get\(`\/api\//g,
      "apiClient.get('/"
    );

    content = content.replace(
      /axios\.post\(`\/api\//g,
      "apiClient.post('/"
    );

    content = content.replace(
      /axios\.put\(`\/api\//g,
      "apiClient.put('/"
    );

    content = content.replace(
      /axios\.patch\(`\/api\//g,
      "apiClient.patch('/"
    );

    content = content.replace(
      /axios\.delete\(`\/api\//g,
      "apiClient.delete('/"
    );

    // 移除config导入（如果不再需要）
    if (!content.includes('config.') && content.includes("import { config } from '../config/environment';")) {
      content = content.replace(
        /import \{ config \} from '\.\.\/config\/environment';\s*\n/g,
        ''
      );
      changed = true;
    }

    if (changed) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ 已修复: ${filePath}`);
    } else {
      console.log(`ℹ️  无需修复: ${filePath}`);
    }

  } catch (error) {
    console.error(`❌ 修复失败 ${filePath}:`, error.message);
  }
}

// 修复所有文件
console.log('开始修复文件...\n');

filesToFix.forEach(fixAxiosUsage);

console.log('\n🎉 修复完成！');
console.log('\n📋 修复内容:');
console.log('✅ 移除了不安全的Accept-Charset请求头');
console.log('✅ 统一使用ApiClient替代直接的axios调用');
console.log('✅ 清理了不必要的导入');

console.log('\n🚀 建议测试:');
console.log('1. 重新启动前端应用');
console.log('2. 检查浏览器控制台是否还有Accept-Charset错误');
console.log('3. 测试登录功能是否正常工作');
