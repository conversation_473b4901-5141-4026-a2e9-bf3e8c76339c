/**
 * 资产服务
 * 负责资产的加载、上传和管理
 */
import { apiClient } from './ApiClient';
import { EventEmitter } from '../utils/EventEmitter';
import { AssetType } from '../store/asset/assetSlice';

// 资产事件类型
export enum AssetEventType {
  LOADING_START = 'loadingStart',
  LOADING_PROGRESS = 'loadingProgress',
  LOADING_COMPLETE = 'loadingComplete',
  LOADING_ERROR = 'loadingError',
  UPLOAD_START = 'uploadStart',
  UPLOAD_PROGRESS = 'uploadProgress',
  UPLOAD_COMPLETE = 'uploadComplete',
  UPLOAD_ERROR = 'uploadError',
  ASSET_ADDED = 'assetAdded',
  ASSET_REMOVED = 'assetRemoved',
  ASSET_CHANGED = 'assetChanged',
  FOLDER_ADDED = 'folderAdded',
  FOLDER_REMOVED = 'folderRemoved',
  FOLDER_CHANGED = 'folderChanged'}

// 资产接口
export interface Asset {
  id: string;
  name: string;
  type: AssetType;
  url: string;
  thumbnail?: string;
  metadata?: any;
  projectId: string;
  folderId?: string;
  createdAt: string;
  updatedAt: string;
}

// 文件夹接口
export interface Folder {
  id: string;
  name: string;
  projectId: string;
  parentId?: string;
  createdAt: string;
  updatedAt: string;
}

// 资产服务类
class AssetService extends EventEmitter {
  private static instance: AssetService;
  
  private currentProjectId: string | null = null;
  private assets: Asset[] = [];
  private folders: Folder[] = [];
  private currentFolder: Folder | null = null;
  
  private constructor() {
    super();
  }
  
  /**
   * 获取资产服务实例
   */
  public static getInstance(): AssetService {
    if (!AssetService.instance) {
      AssetService.instance = new AssetService();
    }
    return AssetService.instance;
  }
  
  /**
   * 设置当前项目
   * @param projectId 项目ID
   */
  public setCurrentProject(projectId: string): void {
    this.currentProjectId = projectId;
    this.assets = [];
    this.folders = [];
    this.currentFolder = null;
  }
  
  /**
   * 获取项目资产
   * @param projectId 项目ID
   * @param folderId 文件夹ID
   */
  public async fetchAssets(projectId: string, folderId?: string): Promise<Asset[]> {
    try {
      this.emit(AssetEventType.LOADING_START, { projectId, folderId });

      const response = await apiClient.get('/assets', {
        params: { projectId, folderId }});

      this.assets = response.data;
      this.currentProjectId = projectId;

      this.emit(AssetEventType.LOADING_COMPLETE, { assets: this.assets, projectId, folderId });

      return this.assets;
    } catch (error) {
      console.error('获取资产失败:', error);
      this.emit(AssetEventType.LOADING_ERROR, { error, projectId, folderId });
      throw error;
    }
  }
  
  /**
   * 获取项目文件夹
   * @param projectId 项目ID
   * @param parentId 父文件夹ID
   */
  public async fetchFolders(projectId: string, parentId?: string): Promise<Folder[]> {
    try {
      const response = await apiClient.get('/assets/folders', {
        params: { projectId, parentId }});

      this.folders = response.data;
      this.currentProjectId = projectId;
      
      return this.folders;
    } catch (error) {
      console.error('获取文件夹失败:', error);
      throw error;
    }
  }
  
  /**
   * 创建文件夹
   * @param name 文件夹名称
   * @param parentId 父文件夹ID
   */
  public async createFolder(name: string, parentId?: string): Promise<Folder> {
    if (!this.currentProjectId) {
      throw new Error('没有活动项目');
    }

    try {
      const response = await apiClient.post('/assets/folders', {
        name,
        projectId: this.currentProjectId,
        parentId});

      const folder = response.data;
      this.folders.push(folder);

      this.emit(AssetEventType.FOLDER_ADDED, folder);

      return folder;
    } catch (error) {
      console.error('创建文件夹失败:', error);
      throw error;
    }
  }
  
  /**
   * 上传资产
   * @param file 文件
   * @param type 资产类型
   * @param folderId 文件夹ID
   */
  public async uploadAsset(file: File, type: AssetType, folderId?: string): Promise<Asset> {
    if (!this.currentProjectId) {
      throw new Error('没有活动项目');
    }
    
    try {
      this.emit(AssetEventType.UPLOAD_START, { file, type, folderId });
      
      const formData = new FormData();
      formData.append('file', file);
      formData.append('type', type);
      formData.append('projectId', this.currentProjectId);
      if (folderId) {
        formData.append('folderId', folderId);
      }

      const response = await apiClient.post('/assets/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'},
        onUploadProgress: (progressEvent) => {
          const percentCompleted = Math.round((progressEvent.loaded * 100) / (progressEvent.total || 1));
          this.emit(AssetEventType.UPLOAD_PROGRESS, { percentCompleted, file, type, folderId });
        }});
      
      const asset = response.data;
      this.assets.push(asset);
      
      this.emit(AssetEventType.UPLOAD_COMPLETE, { asset, file, type, folderId });
      this.emit(AssetEventType.ASSET_ADDED, asset);
      
      return asset;
    } catch (error) {
      console.error('上传资产失败:', error);
      this.emit(AssetEventType.UPLOAD_ERROR, { error, file, type, folderId });
      throw error;
    }
  }
  
  /**
   * 删除资产
   * @param assetId 资产ID
   */
  public async deleteAsset(assetId: string): Promise<void> {
    if (!this.currentProjectId) {
      throw new Error('没有活动项目');
    }
    
    try {
      await apiClient.delete(`/projects/${this.currentProjectId}/assets/${assetId}`);
      
      // 从列表中移除资产
      const index = this.assets.findIndex(asset => asset.id === assetId);
      if (index !== -1) {
        const asset = this.assets[index];
        this.assets.splice(index, 1);
        this.emit(AssetEventType.ASSET_REMOVED, asset);
      }
    } catch (error) {
      console.error('删除资产失败:', error);
      throw error;
    }
  }
  
  /**
   * 删除文件夹
   * @param folderId 文件夹ID
   */
  public async deleteFolder(folderId: string): Promise<void> {
    if (!this.currentProjectId) {
      throw new Error('没有活动项目');
    }
    
    try {
      await apiClient.delete(`/projects/${this.currentProjectId}/folders/${folderId}`);
      
      // 从列表中移除文件夹
      const index = this.folders.findIndex(folder => folder.id === folderId);
      if (index !== -1) {
        const folder = this.folders[index];
        this.folders.splice(index, 1);
        this.emit(AssetEventType.FOLDER_REMOVED, folder);
      }
      
      // 如果当前文件夹被删除，重置当前文件夹
      if (this.currentFolder && this.currentFolder.id === folderId) {
        this.currentFolder = null;
      }
    } catch (error) {
      console.error('删除文件夹失败:', error);
      throw error;
    }
  }
  
  /**
   * 重命名资产
   * @param assetId 资产ID
   * @param newName 新名称
   */
  public async renameAsset(assetId: string, newName: string): Promise<Asset> {
    if (!this.currentProjectId) {
      throw new Error('没有活动项目');
    }
    
    try {
      const response = await apiClient.patch(`/projects/${this.currentProjectId}/assets/${assetId}`, {
        name: newName});
      
      const updatedAsset = response.data;
      
      // 更新列表中的资产
      const index = this.assets.findIndex(asset => asset.id === assetId);
      if (index !== -1) {
        this.assets[index] = updatedAsset;
        this.emit(AssetEventType.ASSET_CHANGED, updatedAsset);
      }
      
      return updatedAsset;
    } catch (error) {
      console.error('重命名资产失败:', error);
      throw error;
    }
  }
  
  /**
   * 重命名文件夹
   * @param folderId 文件夹ID
   * @param newName 新名称
   */
  public async renameFolder(folderId: string, newName: string): Promise<Folder> {
    if (!this.currentProjectId) {
      throw new Error('没有活动项目');
    }
    
    try {
      const response = await apiClient.patch(`/projects/${this.currentProjectId}/folders/${folderId}`, {
        name: newName});
      
      const updatedFolder = response.data;
      
      // 更新列表中的文件夹
      const index = this.folders.findIndex(folder => folder.id === folderId);
      if (index !== -1) {
        this.folders[index] = updatedFolder;
        this.emit(AssetEventType.FOLDER_CHANGED, updatedFolder);
      }
      
      // 如果当前文件夹被重命名，更新当前文件夹
      if (this.currentFolder && this.currentFolder.id === folderId) {
        this.currentFolder = updatedFolder;
      }
      
      return updatedFolder;
    } catch (error) {
      console.error('重命名文件夹失败:', error);
      throw error;
    }
  }
  
  /**
   * 移动资产到文件夹
   * @param assetId 资产ID
   * @param folderId 目标文件夹ID，如果为null则移动到根目录
   */
  public async moveAsset(assetId: string, folderId: string | null): Promise<Asset> {
    if (!this.currentProjectId) {
      throw new Error('没有活动项目');
    }
    
    try {
      const response = await apiClient.patch(`/projects/${this.currentProjectId}/assets/${assetId}`, {
        folderId});
      
      const updatedAsset = response.data;
      
      // 更新列表中的资产
      const index = this.assets.findIndex(asset => asset.id === assetId);
      if (index !== -1) {
        this.assets[index] = updatedAsset;
        this.emit(AssetEventType.ASSET_CHANGED, updatedAsset);
      }
      
      return updatedAsset;
    } catch (error) {
      console.error('移动资产失败:', error);
      throw error;
    }
  }
  
  /**
   * 移动文件夹
   * @param folderId 文件夹ID
   * @param parentId 目标父文件夹ID，如果为null则移动到根目录
   */
  public async moveFolder(folderId: string, parentId: string | null): Promise<Folder> {
    if (!this.currentProjectId) {
      throw new Error('没有活动项目');
    }
    
    try {
      const response = await apiClient.patch(`/projects/${this.currentProjectId}/folders/${folderId}`, {
        parentId});
      
      const updatedFolder = response.data;
      
      // 更新列表中的文件夹
      const index = this.folders.findIndex(folder => folder.id === folderId);
      if (index !== -1) {
        this.folders[index] = updatedFolder;
        this.emit(AssetEventType.FOLDER_CHANGED, updatedFolder);
      }
      
      // 如果当前文件夹被移动，更新当前文件夹
      if (this.currentFolder && this.currentFolder.id === folderId) {
        this.currentFolder = updatedFolder;
      }
      
      return updatedFolder;
    } catch (error) {
      console.error('移动文件夹失败:', error);
      throw error;
    }
  }
  
  /**
   * 设置当前文件夹
   * @param folder 文件夹
   */
  public setCurrentFolder(folder: Folder | null): void {
    this.currentFolder = folder;
  }
  
  /**
   * 获取当前文件夹
   */
  public getCurrentFolder(): Folder | null {
    return this.currentFolder;
  }
  
  /**
   * 获取资产列表
   */
  public getAssets(): Asset[] {
    return [...this.assets];
  }
  
  /**
   * 获取文件夹列表
   */
  public getFolders(): Folder[] {
    return [...this.folders];
  }
  
  /**
   * 获取当前项目ID
   */
  public getCurrentProjectId(): string | null {
    return this.currentProjectId;
  }
  
  /**
   * 销毁资产服务
   */
  public dispose(): void {
    this.assets = [];
    this.folders = [];
    this.currentFolder = null;
    this.currentProjectId = null;
    this.removeAllListeners();
  }
}

export default AssetService.getInstance();
