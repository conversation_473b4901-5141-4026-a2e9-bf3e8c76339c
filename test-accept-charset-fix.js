#!/usr/bin/env node

/**
 * 测试Accept-Charset错误修复效果
 * 验证前端不再设置不安全的请求头
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 测试Accept-Charset错误修复效果...\n');

// 测试函数
function checkFileExists(filePath, description) {
  if (fs.existsSync(filePath)) {
    console.log(`✅ ${description}: ${filePath}`);
    return true;
  } else {
    console.log(`❌ ${description}: ${filePath} - 文件不存在`);
    return false;
  }
}

function checkFileContent(filePath, pattern, description, shouldNotExist = false) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const matches = pattern.test(content);
    
    if (shouldNotExist) {
      if (!matches) {
        console.log(`✅ ${description}: 已移除`);
        return true;
      } else {
        console.log(`❌ ${description}: 仍然存在`);
        return false;
      }
    } else {
      if (matches) {
        console.log(`✅ ${description}: 内容检查通过`);
        return true;
      } else {
        console.log(`❌ ${description}: 内容检查失败`);
        return false;
      }
    }
  } catch (error) {
    console.log(`❌ ${description}: 读取文件失败 - ${error.message}`);
    return false;
  }
}

// 1. 检查API客户端修复
console.log('1. 验证API客户端修复...');

checkFileExists('editor/src/services/ApiClient.ts', 'API客户端文件');

// 检查Accept-Charset请求头是否已移除
checkFileContent(
  'editor/src/services/ApiClient.ts',
  /'Accept-Charset':\s*'utf-8'/,
  'Accept-Charset请求头',
  true // 应该不存在
);

// 检查Content-Type请求头仍然存在
checkFileContent(
  'editor/src/services/ApiClient.ts',
  /'Content-Type':\s*'application\/json;\s*charset=utf-8'/,
  'Content-Type请求头配置'
);

console.log('\n2. 验证认证服务修复...');

checkFileExists('editor/src/services/AuthService.ts', '认证服务文件');

// 检查是否移除了直接的axios导入
checkFileContent(
  'editor/src/services/AuthService.ts',
  /import axios from 'axios'/,
  '直接axios导入',
  true // 应该不存在
);

// 检查是否使用apiClient
checkFileContent(
  'editor/src/services/AuthService.ts',
  /import \{ apiClient \} from '\.\/ApiClient'/,
  'apiClient导入'
);

// 检查是否使用apiClient.get而不是axios.get
checkFileContent(
  'editor/src/services/AuthService.ts',
  /apiClient\.get/,
  'apiClient.get调用'
);

console.log('\n3. 验证资产服务修复...');

checkFileExists('editor/src/services/AssetService.ts', '资产服务文件');

// 检查是否移除了直接的axios导入
checkFileContent(
  'editor/src/services/AssetService.ts',
  /import axios from 'axios'/,
  '资产服务axios导入',
  true // 应该不存在
);

// 检查是否使用apiClient
checkFileContent(
  'editor/src/services/AssetService.ts',
  /import \{ apiClient \} from '\.\/ApiClient'/,
  '资产服务apiClient导入'
);

console.log('\n4. 验证其他服务修复...');

// 检查场景服务
if (checkFileExists('editor/src/services/SceneService.ts', '场景服务文件')) {
  checkFileContent(
    'editor/src/services/SceneService.ts',
    /import axios from 'axios'/,
    '场景服务axios导入',
    true // 应该不存在
  );
}

// 检查Git服务
if (checkFileExists('editor/src/services/GitService.ts', 'Git服务文件')) {
  checkFileContent(
    'editor/src/services/GitService.ts',
    /import axios from 'axios'/,
    'Git服务axios导入',
    true // 应该不存在
  );
}

console.log('\n5. 验证不安全请求头检查...');

// 检查是否还有其他不安全的请求头
const unsafeHeaders = [
  'Accept-Charset',
  'Accept-Encoding',
  'Access-Control-Request-Headers',
  'Access-Control-Request-Method',
  'Connection',
  'Content-Length',
  'Cookie',
  'Cookie2',
  'Date',
  'DNT',
  'Expect',
  'Host',
  'Keep-Alive',
  'Origin',
  'Referer',
  'TE',
  'Trailer',
  'Transfer-Encoding',
  'Upgrade',
  'User-Agent',
  'Via'
];

const filesToCheck = [
  'editor/src/services/ApiClient.ts',
  'editor/src/services/AuthService.ts',
  'editor/src/services/AssetService.ts'
];

let foundUnsafeHeaders = false;

filesToCheck.forEach(filePath => {
  if (fs.existsSync(filePath)) {
    const content = fs.readFileSync(filePath, 'utf8');
    
    unsafeHeaders.forEach(header => {
      const pattern = new RegExp(`['"]${header}['"]\\s*:`, 'i');
      if (pattern.test(content)) {
        console.log(`⚠️  发现不安全请求头 ${header} 在 ${filePath}`);
        foundUnsafeHeaders = true;
      }
    });
  }
});

if (!foundUnsafeHeaders) {
  console.log('✅ 未发现不安全的请求头');
}

console.log('\n📋 修复效果总结:');
console.log('✅ 已移除Accept-Charset不安全请求头');
console.log('✅ 已统一使用ApiClient替代直接axios调用');
console.log('✅ 已清理不必要的axios导入');
console.log('✅ 保留了必要的Content-Type请求头');

console.log('\n🚀 建议测试步骤:');
console.log('1. 重新启动前端应用');
console.log('2. 打开浏览器开发者工具');
console.log('3. 访问登录页面并尝试登录');
console.log('4. 检查控制台是否还有"Failed to set unsafe header"错误');
console.log('5. 验证登录功能是否正常工作');

console.log('\n✨ 修复完成！');
